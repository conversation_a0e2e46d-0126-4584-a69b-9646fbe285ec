<?php

namespace App\Http\Controllers\Ajax;

//use DB;
use App\Enums\Language;
use App\Enums\UserRoll;
use App\Http\Controllers\Controller;
use App\Http\Traits\StoreImageTrait;
use App\Model\CartContent;
use App\Model\CartPackage;
use App\Model\Group;
use App\Model\GroupType;
use App\Model\Product;
use App\Model\RecordCause;
use App\Model\Staff;
use App\Model\StaffInGroup;
use App\Model\Treatment_custom_image;
use App\Model\Treatment_users_image;
use App\Model\User;
use App\Model\UserOption;
use App\Services\Treatment\SessionIdManager;
use App\Services\Users\UserService;
use Carbon\Carbon;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Session;

class SajaxController extends Controller
{

    use StoreImageTrait;
    /**
     * Switch Therapist to there Subuser
     * @param Sub-user id
     * @return Successfully switch
     */
    public function switch_user()
    {
        $id = request()->id;
        switchUser($id);
        // $admin_name = getAuthUserDetails()->first_name.' '.getAuthUserDetails()->last_name;
        // $user_name = getUserDetails()->first_name.' '.getUserDetails()->last_name;

        // activityLogTrack(4, "{$admin_name} switched to subuser {$user_name}", "{$admin_name} Auswewählter Unterbenutzer {$user_name}", 'switch_user');#AL

        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * Search user by keyword
     * @pram keyword
     * @return array_list
     */
    public function search_user(Request $req)
    {
        $search_value = $req->search_key;

        if (empty($search_value) && empty($req->group_id)) {
            return json_encode(false);
        }

        $auth = Auth::user();

        $search_query = User::with('group')->where('boss_id', getAuthID());

        if ($req->group_id === 'staff') {
            return json_encode($auth->staffs);
        }

        if ($auth->user_type == 4) {
            $search_query->whereIn('id', getCollectionArrayForStaffUser($auth->staffInGorup));
        }

        $search_query->select('users.id', DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'first_name', 'last_name', 'email', 'photo', 'gender', 'gebdatum','user_type', 'select_user_view');

        if ($req->group_id > 0) {
            $search_query->whereHas('group', function ($group) use ($req) {
                $group->where("group_id", $req->group_id);
            });
        }

        if (!empty($search_value)) {
            $search_query->where(function ($query) use ($search_value) {
                $query->where(DB::raw("CONCAT(first_name, ' ', last_name) "), 'LIKE', '%' . $search_value . '%')
                    ->orWhere(DB::raw("CONCAT(last_name, ' ', first_name) "), 'LIKE', '%' . $search_value . '%')
                    ->orWhere('email', 'LIKE', '%' . $search_value . '%')
                    ->orWhere('user_name', 'LIKE', '%' . $search_value . '%');
            });
        }

        $search_query->orderBy('id', 'asc')->limit(30);

        $result = $search_query->get()->map(function ($user) {
            $user->gebdatum = getEfitDateFormat($user->gebdatum);
            return $user;
        });

        return json_encode($result);
    }



    // Change product analysis blade top change Year Or month
    public function changeYearMonth()
    {
        $check = request()->value;
        $user = User::find(getUserId());

        $proid = request()->proid;
        $subid = request()->subid;

        $thema_speichern = str_replace(' ', '', $user->thema_speichern ?? '');
        $calculation_with = ($user->calculation_with) ?? '';
        $datumcore = $user->datumcore ?? '';
        $dynamic_cache_key = 'analysis_' . $user->id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;

        if (Cache::has($dynamic_cache_key)) {
            Cache::forget($dynamic_cache_key);
        }
        DB::table('user_options')->where("user_id", getUserId())->update(['ran_ana' => false]);
        $user->calculation_with =  ($check == 'true' || $check == 1) ? true : false;
        $user->update();
        if (!empty($user->update())) {
            return response()->json([
                'success' => true,
                'message' => trans('action.setting_saved_succfully')
            ]);
        }

    }
    // Change view of product by datumcore Users
    public function changeDatumcore()
    {
        $check = request()->check;
        $date = request()->date;
        $user = User::find(getUserId());
        $user->datumcore =  ($check == 'Save') ? getEfitDateFormat($date) : Carbon::now()->format('Y-m-d');
        $user->update();
        if (!empty($user->update())) {
            return response()->json([
                'success'   => true,
                'date'  => date_change($user->datumcore),
                'message' => trans('action.setting_saved_succfully')
            ]);
        }
    }
    // Change product analysis blade top change Year Or month
    public function changeShowFilter()
    {
        $filter_id = request()->filter_id;
        $user = User::with('userfilter')->where('id', getUserId())->first();
        $user->userfilter->update([
            "filter_type"   => $filter_id
        ]);

        if (!empty($user->update())) {
            return response()->json([
                'success'   => true,
                'user'      => $user,
                'message'   => trans('action.setting_saved_succfully')
            ]);
        }
    }

    // Add efit_causes_tbl data
    public function saveToday()
    {

        if (!RecordCause::where('user_id', getUserid())->where('date', date("Y-m-d"))->where('causes_id', request()->cau_id)->exists()) {
            $newData =  new RecordCause;
            $newData->user_id = getUserId();
            $newData->causes_id = request()->cau_id;
            $newData->type = request()->type;
            $newData->date = Carbon::now()->format('Y-m-d');
            $newData->save();
            $newData->analyses()->attach(request()->ana_id);
            $newData->submenus()->attach(request()->sub_id);
        }

        return response()->json(['message', trans('action.successfully_update')]);
    }

    // Add Into Cart From selected Package
    function packageAddToCart()
    {
        if (!UserService::checkUserAccess()) return response()->json([
            'success' => false,
            '_alert_type' => 'warning',
            '_alert' => trans('action.warning'),
            'message' => trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
        ]);
        $getCartContent = CartPackage::with('cartcontents')->find(request()->id);
        foreach ($getCartContent->cartcontents as $key => $value) {
            if(getCartCount() > 199){
                    return response()->json([
                    'success' => false,
                    'cartCount' =>getCartCount(),
                    '_alert_type' =>'warning',
                    '_alert' =>trans('action.warning'),
                    'message' => trans('action.cart_max_allow_alert')
                ]);
            }
            $type_id = '';
            $price = $value->price;
            switch (request()->type) {
                case 'Causes':
                    $type_id     = request()->causes_id;
                    break;
                case 'Medium':
                    $type_id     = request()->medium_id;
                    break;
                case 'Tipp':
                    $type_id       = request()->tipp_id;
                    break;
                case 'Einfluss':
                    $type_id   = request()->causes_id;
                    break;
                case 'Fokus':
                    $type_id      = request()->causes_id;
                    break;
                case 'Analysis':
                    $type_id   = request()->ana_id;
                    break;

                default:
                    $type_id          = 1;
                    break;
            }

            $cart['userID']         = getUserId();
            $cart['analysisID']     = $value->analyse_id;
            $cart['analysisName']   = $value->name;
            $cart['submenu_id']     = $value->submenu_id;
            $cart['productID']      = $value->product_id;
            $cart['calculation']    = $value->calculation;
            $cart['male']           = $value->male;
            $cart['heart']          = $value->heart;
            $cart['price']          = $price;
            $cart['causes_id']      = $value->causes_id;
            $cart['medium_id']      = $value->medium_id;
            $cart['tipp_id']        = $value->tipp_id;
            $cart['color']          = $value->color;
            $cart['type']           = $value->type;
            $cart['others']         = $value->others;
            Cart::instance('wishlist')->add(['id' =>  getUserId(), 'name' => ($value->type == '') ? 'Cart' : "$value->type", 'qty' => ($value->type == 'Analysis') ? $value->analyse_id : 1, 'price' => ($type_id == '') ? 1 : $type_id, 'weight' => 1, 'options' => $cart]);
        }
        return response()->json([
            'success' => true,
            '_alert_type' =>'success',
            '_alert' =>trans('action.success'),
            'message' => trans('action.cart_save'),
            'cart_id' => Cart::content()
        ]);
    }

    // Delete Package from The package list
    function deletePackage()
    {
        if(is_array(request()->ids) && !empty(request()->ids)){
            foreach (request()->ids as $key => $id) {
                $cartpkg = CartPackage::with('cartcontents')->find($id);

                if($cartpkg->cartcontents && $cartpkg->cartcontents->count()){
                    $cartpkg->cartcontents()->detach();#detach All cart Content
                    $cartpkg->users()->detach();#Detach user
                }
                $cartpkg->delete();#delete package
            }
        }else{
            $cartpkg = CartPackage::with('cartcontents')->find(request()->ids);
            if($cartpkg->cartcontents && $cartpkg->cartcontents->count()){
                $cartpkg->cartcontents()->detach();#detach All cart Content
                $cartpkg->users()->detach();#Detach user
            }
            $cartpkg->delete();#delete package
        }

        // activityLogTrack(3, "{$cartpkg->package_name} at Cart Package", "{$cartpkg->package_name} um Behandlungskorb paket", 'cart_package');#AL
        return response()->json([
            'success' => true,
            'message' => trans('action.package_deleted_successfully'),
            'cart_id' => request()->ids
        ]);
    }


    function addNotePKG()
    {
        $cart = CartPackage::find(request()->id);
        $cart->note =  request()->note;
        $cart->update();
        if (!empty($cart->update())) {
            return response()->json([
                'success' => true,
                'cart_id' => $cart,
                'message' => trans('action.note_save')
            ]);
        }
    }
    // add Analysis on cart
    public function add2Cart()
    {
        if (!UserService::checkUserAccess()) return response()->json([
            'success' => false,
            '_alert_type' => 'warning',
            '_alert' => trans('action.warning'),
            'message' => trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
        ]);

        if(getCartCount() > 199)return response()->json([
            'success' => false,
            '_alert_type' =>'warning',
            '_alert' =>trans('action.warning'),
            'message' => trans('action.cart_max_allow_alert')
        ]);
        if(request()->price ==  null && request()->time == null){
            $priceDetails = biorythVisibleDetails();
            $price = rand($priceDetails->gs_min_price, $priceDetails->gs_max_price);
        } elseif (request()->type === 'Topic' && request()->time != null && request()->frequency != null) {
            $price = request()->time;
            $data['frequency'] = request()->frequency;
        } else {
            $price = request()->price;
        }

        switch (request()->type) {
            case 'Causes':
                $type_id     = request()->causes_id;
                if(request()->name == null) request()->name = DB::table((Lang::locale() == '' || Lang::locale() == 'de')?'causes':Lang::locale().'_causes')->select('title')->find($type_id)->title;
                break;
            case 'Medium':
                $type_id     = request()->medium_id;
                if(request()->name == null) request()->name = DB::table((Lang::locale() == '' || Lang::locale() == 'de')?'causes':Lang::locale().'_causes')->select('title')->find($type_id)->title;
                break;
            case 'Tipp':
                $type_id       = request()->tipp_id;
                if(request()->name == null) request()->name = DB::table((Lang::locale() == '' || Lang::locale() == 'de')?'causes':Lang::locale().'_causes')->select('title')->find($type_id)->title;
                break;
            case 'Einfluss':
                $type_id   = request()->causes_id;
                if(request()->name == null) request()->name = DB::table((Lang::locale() == '' || Lang::locale() == 'de')?'causes':Lang::locale().'_causes')->select('title')->find($type_id)->title;
                break;
            case 'Fokus':
                $type_id      = request()->causes_id;
                if(request()->name == null) request()->name = DB::table((Lang::locale() == '' || Lang::locale() == 'de')?'causes':Lang::locale().'_causes')->select('title')->find($type_id)->title;
                break;
            case 'Analysis':
                $type_id   = request()->ana_id;
                $subid = explode('-', request()->submenu_id);
                if (request()->name == null) {
                    $name = DB::table((Lang::locale() == '' || Lang::locale() == 'de' || $subid[0] == 'own') ? 'analyses' : Lang::locale() . '_analyses')
                        ->select('name')
                        ->find($type_id)
                        ->name;
                    request()->merge(['name' => $name]);
                }
                break;

            default:
                $type_id          = 1;
                break;
        }

        $data['userID']         = getUserId();
        $data['analysisID']     = (empty(request()->ana_id)) ? 0 : request()->ana_id;
        $data['analysisName']   = request()->frequency ? request()->name . ' ( ' . request()->frequency. ' Hz )' : request()->name;
        $data['submenu_id']     = (empty(request()->submenu_id)) ? 0 : request()->submenu_id;
        $data['productID']      = request()->proID;
        $data['calculation']    = (empty(request()->calculation)) ? 0 : request()->calculation;
        $data['male']           = (empty(request()->male)) ? 0 : request()->male;
        $data['heart']          = (empty(request()->heart)) ? 0 : request()->heart;
        $data['price']          = $price;
        $data['causes_id']      = (empty(request()->causes_id)) ? 0 : request()->causes_id;
        $data['medium_id']      = (empty(request()->medium_id)) ? 0 : request()->medium_id;
        $data['tipp_id']        = (empty(request()->tipp_id)) ? 0 : request()->tipp_id;
        $data['color']          = (empty(request()->color)) ? null : request()->color;
        $data['type']           = request()->type;
        $data['others']         = request()->others ?? [];

        $insertdata = Cart::instance('wishlist')->add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : request()->type, 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => ($type_id == '') ? 1 : $type_id, 'weight' => 1, 'options' => $data]);
        $getData = Cart::get($insertdata->rowId);
        return response()->json([
            'success' => true,
            'cart_id' => $insertdata->rowId,
            'cart_data' => $getData->options->toArray(),
            'result' => gmdate('i:s', $data['price'])
        ]);
    }

    // add Analysis on cart
    public function add2CartAnalysis()
    {
        if(getCartCount() > 199)return response()->json([
            'success' => false,
            '_alert_type' =>'warning',
            '_alert' =>trans('action.warning'),
            'message' => trans('action.cart_max_allow_alert')
        ]);
        if(empty(request()->price)){
            $priceValue = biorythVisibleDetails();
            $price  = rand($priceValue->gs_min_price, $priceValue->gs_max_price);
        }else $price = request()->price;


        $data['userID']         = getUserId();
        $data['analysisID']     = request()->ana_id;
        $data['analysisName']   = request()->ana_name;
        $data['productID']      = request()->proID;
        $data['calculation']    = request()->calculation;
        $data['male']           = request()->male;
        $data['heart']          = request()->heart;
        $data['price']          = $price;
        $data['causes_id']      = (empty(request()->causes_id)) ? 0 : request()->causes_id;
        $data['type']           = request()->type;

        $insertdata = Cart::add(['id' =>  getUserId(), 'name' => request()->type, 'qty' => request()->ana_id, 'price' => (empty(request()->price)) ? 1 : request()->price, 'weight' => 1, 'options' => $data]);
        $getData = Cart::get($insertdata->rowId);
        return response()->json([
            'success' => true,
            'cart_id' => $insertdata->rowId,
            'cart_data' => $getData->options->toArray(),
            'result' => gmdate('i:s', $data['price'])
        ]);
    }


    // add Cause Medium Tipp on cart
    public function add2CartCMT()
    {
        if(getCartCount() > 199)return response()->json([
            'success' => false,
            '_alert_type' =>'warning',
            '_alert' =>trans('action.warning'),
            'message' => trans('action.cart_max_allow_alert')
        ]);
        if (empty(request()->price)) {
            $priceValue = biorythVisibleDetails();
            $price  = rand($priceValue->gs_min_price, $priceValue->gs_max_price);
        } else $price = request()->price;

        $data['userID']         = getUserId();
        $data['analysisID']     = request()->ana_id;
        $data['analysisName']   = request()->ana_name;
        $data['productID']      = request()->proID;
        $data['calculation']    = request()->calculation;
        $data['male']           = request()->male;
        $data['heart']          = request()->heart;
        $data['price']          = $price;
        $data['causes_id']      = (empty(request()->causes_id)) ? 0 : request()->causes_id;
        $data['type']           = request()->type;

        $insertdata = Cart::add(['id' =>  getUserId(), 'name' => request()->type, 'qty' => (empty(request()->causes_id)) ? request()->ana_id : request()->causes_id, 'price' => (empty(request()->price)) ? 1 : request()->price, 'weight' => 1, 'options' => $data]);
        $getData = Cart::get($insertdata->rowId);
        return response()->json([
            'success' => true,
            'cart_id' => $insertdata->rowId,
            'cart_data' => $getData->options->toArray(),
            'result' => gmdate('i:s', $data['price'])
        ]);
    }
    #Clear All cart data
    public function clearCart()
    {
        Cart::instance('wishlist')->destroy();
        return response()->json([
            'success' => true,
            '_alert_type' =>'success',
            '_alert' =>trans('action.success_msg'),
            'message' => trans('action.cart_clear_successfully'),
            'cart'    => 0
        ]);
    }

    // Remove Cart Data
    public function removeCart()
    {
        $rowId = request()->cart_id;
        $getData = Cart::instance('wishlist')->get($rowId);
        $result = $getData->options->toArray();
        Cart::remove($rowId);
        return response()->json([
            'success' => true,
            '_alert_type' =>'success',
            '_alert' =>trans('action.success_msg'),
            'message' => trans('action.removed_successfully'),
            'cart'    => $result
        ]);
    }

    public function changeDashView()
    {
        $value      = request()->value ?? 22;
        DB::table('user_options')->where('user_id', request()->user_id)->update(['dashboard_option' => $value]);

        return response()->json([
            'success' => true
        ]);
    }

    public function saveTopic(Request $request)
    {
        try {
            $topic  = $request->content;
            $user = DB::table('users')->where('id', getUserId())->update(['thema_speichern' =>  $topic]);

            return response()->json([
                'success' => $user
            ]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false]);
        }

    }

    // Show menu click data on the dashboard

    function showMenusClick()
    {
        $product_id  = request()->id;
        $submenuList = Product::with(['submenu' => function ($query) {
            $query->limit(4);
        }])->where('id', $product_id)->first();
        $data['product_name'] = $submenuList->product_name;
        $data['submenu'] = $submenuList['submenu']->toArray();
        return response()->json(
            [
                'success' => true,
                'data'  => $data
            ]
        );
    }

    public function getPackageContent(){
        $id = request()->id;
        $array = [];
        $contents = CartPackage::with('cartcontents')->find($id);
        foreach ($contents->cartcontents as $key => $content) {
            if(!$content->name) {
                continue;
            }

            $array[] =  gmdate("i:s", $content->price)." ".$content->name;
        }

        return response()->json(
            [
                'success' => true,
                'contents'  => $array
            ]
        );
    }

    // Save PAckage name For Remote treatment
    function savePackageCart()
    {
        #check Package name exist or not
        date_default_timezone_set('Europe/Vienna');
        $getCartData = getCartData();
        if (empty($getCartData)) return response()->json([
            'success' => true,
        ]);
        // $packageList = User::with(['cartpackages' => function ($query) {
        //     $query->where('package_name', request()->package_name);
        // }])->find(getUserId());
        #package id get
        // $package_id = $packageList->cartpackages[0]->id;
        #update package is available
        // if(!empty($package_id)){
        //     CartPackage::find($package_id)->update(array('created_at' => date("Y-m-d H:i:s")));
        // }
        #If Cron Cart Not exist it added
        // if ($packageList->cartpackages[0]->id == null) {
        $package =  new CartPackage;
        $package->package_name = request()->package_name;
        $package->save();
        // activityLogTrack(1, "{$package->package_name} at Cart Package", "{$package->package_name} um Behandlungskorb paket", 'cart_package');#AL
        $package->users()->attach(getUserId());
        $package_id =  $package->id;
        // }
        #Old Cron Delete
        // $cartpkg = CartPackage::with('cartcontents')->find($package_id);
        // $cartContent = $cartpkg->cartcontents()->detach();#delete all old cart content
        #cart content
        foreach ($getCartData as $key => $cart) {
            $__data = $cart->options->toArray();
            $cartContent = new CartContent();
            $cartContent->product_id     = $__data['productID'];
            $cartContent->analyse_id     = $__data['analysisID'];
            $cartContent->submenu_id     = $__data['submenu_id'];
            $cartContent->price          = $__data['price'];
            $cartContent->calculation    = $__data['calculation'];
            $cartContent->male           = $__data['male'];
            $cartContent->heart          = $__data['heart'];
            $cartContent->name           = $__data['analysisName'];
            $cartContent->causes_id      = $__data['causes_id'];
            $cartContent->medium_id      = $__data['medium_id'];
            $cartContent->tipp_id        = $__data['tipp_id'];
            $cartContent->color          = $__data['color'];
            $cartContent->type           = $__data['type'];
            if (!empty($__data)) {
                if ($cartContent->save()) { #data Save to Cart Content Table
                    $cartContent->cartpackges()->attach($package_id);
                    Cart::remove($cart->rowId); #Remove Cart data
                }
            }
        }
        return response()->json([
            'success' => true,
            'result' => "Inserted"
        ]);
    }

    function saveCartToPackage()
    {

        #check Package name exist or not
        date_default_timezone_set('Europe/Vienna');
        $getCartData = getCartData();
        if (empty($getCartData)) return response()->json([
            'success' => true,
        ]);
        if (Cache::has(('treatment' . getAuthId()))) Cache::forget('treatment' . getAuthId());
        $package =  new CartPackage;
        $package->package_name = request()->package_name;
        $package->unique_id = SessionIdManager::invalidateSessionId();
        $package->save();

        $package->users()->attach(getUserId());
        $package_id =  $package->id;
        // activityLogTrack(1, "{$package->package_name} at Cart Package by {$fname} {$lname}", "{$package->package_name} um Behandlungskorb paket by {$fname} {$lname}", 'cart_package');#AL

        #cart content
        foreach ($getCartData as $key => $cart) {
            $__data = $cart->options->toArray();
            $cartContent = new CartContent();
            $cartContent->product_id     = $__data['productID'];
            $cartContent->analyse_id     = $__data['analysisID'];
            $cartContent->submenu_id     = $__data['submenu_id'];
            $cartContent->price          = $__data['price'];
            $cartContent->calculation    = min(100, $__data['calculation']);
            $cartContent->male           = $__data['male'];
            $cartContent->heart          = $__data['heart'];
            $cartContent->name           = $__data['analysisName'];
            $cartContent->causes_id      = $__data['causes_id'];
            $cartContent->medium_id      = $__data['medium_id'];
            $cartContent->tipp_id        = $__data['tipp_id'];
            $cartContent->color          = $__data['color'];
            $cartContent->type           = $__data['type'];
            if (!empty($__data)) {
                if ($cartContent->save()) { #data Save to Cart Content Table
                    $cartContent->cartpackges()->attach($package_id);
                    // Cart::remove($cart->rowId);#Remove Cart data
                }
            }
        }
        return response()->json([
            'success' => true,
            'result' => "Inserted",
            'message' => trans('action.cart_save')
        ]);
    }

    public function addNewStaff()
    {
        $status = false;
        $users = [];

        if(is_array(request()->userID)){
            foreach (request()->userID as $userid) {
                $user = User::findOrFail($userid);
                if ($user) {
                    if($user->staff()->first() == null){
                        $users[] = $user;
                        $user->update(['user_type'=> 4]);
                        $status = true;
                        $user->staff()->create(['creator_id' => getAuthID(), 'user_id' => $user->id]);
                    }
                }
            }
        }else{
            $user = User::findOrFail(request()->userID);
            if ($user) {
                if ($user->staff()->first() == null) {
                    $users[] = $user;
                    $user->update(['user_type'=> 4]);
                    $status = true;
                    $user->staff()->create(['creator_id' => getAuthID(), 'user_id' => $user->id]);
                };
            }
        }

        return response()->json([
            'success' => $status,
            'users' => $users
        ]);
    }
    public function addGroupMember()
    {
        $status = false;
        $users = [];
        if(is_array(request()->userID)){
            foreach (request()->userID as $userid) {
                $user = User::findOrFail($userid);
                if ($user) {
                    $user->group()->sync(request()->groupID);
                    $users[] = $user;
                    $status = true;
                }
            }
        }else{
            $user = User::findOrFail(request()->userID);
            if ($user) {
                $user->group()->sync(request()->groupID);
                $status = true;
                $users[] = $user;
            }
        }

        return response()->json([
            'success' => $status,
            'users' => $users
        ]);
    }

    function addUserOwnGroup()
    {
        $gorup_name = request()->group_name;
        $default = GroupType::where('type_name', 'Deafult')->first();
        $id = $default->id;
        if (empty($default)) {
            $addDefault =  new GroupType;
            $addDefault->type_name = "Deafult";
            $addDefault->save();
            $id = $addDefault->id;
        }

        $group = new Group;
        $group->group_name =  $gorup_name;
        $group->group_type_id = $id;
        $group->save();
        // activityLogTrack(1, "{$group->group_name} at Group Module", "{$group->group_name} um Gruppen Modul", 'group');#AL

        $group->users()->attach(getUserId());

        return response()->json([
            'success' => true,
            'group' => $gorup_name
        ]);
    }

    function userThemaSpich()
    {
        $thema_speichern = request()->topic;

        $user = User::find(getUserId());
        $user->thema_speichern =  $thema_speichern;
        $user->update();
        // activityLogTrack(2, " at Thema Speichern ", " um Thema Einstellung ", 'dashboard');#AL
        if (!empty($user->update())) {
            return response()->json([
                'success' => true
            ]);
        }
    }

    function updateFillStatus()
    {
        $user = User::findOrFail(request()->user_id);
        if (!empty(request()->dob) && !empty(request()->pob)) {
            $user->update(array('gebdatum' => getEfitDateFormat(request()->dob), 'gebort' => request()->pob));
            return back();
            // return redirect(route('dashboard.dashboard'));
        } else {
            return view('auth.fill_status');
        }
    }

    public function deleteGroup()
    {
        $group = Group::findOrFail(request()->group_id);
        $group->delete();
        // activityLogTrack(3, "{$group->group_name} at Group Module", "{$group->group_name} um Gruppen Modul", 'group');#AL
        return response()->json([
            'success' => true
        ]);
    }

    public function deleteSubUser()
    {
        $user_id = request()->id;
        $user = User::findOrFail($user_id);
        $user->groups()->delete();
        $user->userfilter()->delete();
        $user->useraccount()->delete();
        $user->useroption()->delete();
        $user->frabklangSubmenuWatchRecord()->delete();

        $this->deleteImageFile($user->photo, public_path('profile/users'));

        $user->delete();

        if(Session::has('id'))
            session()->forget('id');

        session()->put('id',Auth::id());

        #remove user cache
        removeUserCacheKeys(getAuthID());

        activityLogTrack(3, "{$user->first_name} {$user->last_name} at User Module", "{$user->first_name} {$user->last_name} um Benutzer Modul", 'User Module', 'users');#AL
        return response()->json([
            'success' => true
        ]);
    }

    #remove pdf logo
    public function removePDFLogo()
    {
        $userOption = UserOption::findOrFail(request()->id);
        $logo_name = $userOption->pdf_logo;
        $userOption->pdf_logo =  null;
        $userOption->update();
        // activityLogTrack(3, "{$logo_name} at PDF Logo", "{$logo_name} um PDF Logo", 'user');#AL
        return response()->json([
            'success' => true
        ]);
    }
    #chnage background images for treatment
    public function changebgImage()
    {
        $img = request()->id;
        $user_id = request()->user_id ?? getAuthID();
        $bgcheck = Treatment_users_image::where('user_id', $user_id)->exists();
        if(!$bgcheck){
            Treatment_users_image::where('user_id', $user_id)
            ->updateOrCreate(array('user_id'=> $user_id,'treatment_custom_image_id' => ($img == "Random" || $img == '') ? null : $img, 'rn_status' => ($img == "Random") ? true : false));
        }else{
            Treatment_users_image::where('user_id', $user_id)
            ->update(array('treatment_custom_image_id' => ($img == "Random" || $img == '') ? null : $img, 'rn_status' => ($img == "Random") ? true : false));
        }
        $random = null;
        if ($img == "Random") $random = Treatment_custom_image::all()->random(1)->first()->image;

        return response()->json([
            'image' => $random != null ? asset($random) : asset('images/circle_new.gif'),
            'message' => __('action.successfullychange'),
            'success' => true,
            'user'=> $user_id
        ]);
    }

    /**
     * Clear login User Cache function
     */
    public function cache_clear(){
        Artisan::call('view:clear'); //keep it at the top otherwise view:clear will not work
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('optimize:clear');
        $notification = array(
            'message' => __('action.success_cache_clear'),
            'alert-type' => "success"
        );
        return back()->with($notification);

    }
    /**
     * change Reaction Status for user
     * DB user_options,Column ra_status
     * @return success status
     */
    public function change_reaction_status(Request $req){
        $useroption = UserOption::where('user_id', getUserId())->first();
        if($useroption){
            $status = ($useroption->ra_status == true) ? false : true;
            $useroption->ra_status = $status;
            $useroption->update();
        }else{
            $useroption = new UserOption();
            $status = ($useroption->ra_status == true) ? false : true;
            $useroption->user_id = getUserId();
            $useroption->ra_status = $status;
            $useroption->save();
        }
        return response()->json([
            'success' => true,
            'status' => $status,
            'message' => trans('action.option_changed')
        ]);
    }
    /**
     * change Reaction Status for user
     * DB user_options,Column ra_pdf_status
     * @return success status
     */
    public function change_reaction_pdf_show(Request $req){
        $useroption = UserOption::where('user_id', getUserId())->first();
        $status = ($useroption->ra_pdf_status == true) ? false : true;
        $useroption->ra_pdf_status = $status;
        $useroption->update();
        return response()->json([
            'success' => true,
            'status' => $status,
            'message' => trans('action.option_changed')
        ]);
    }

    /**
     * Delete record from db_table
     * @param id, @param tbl_name,  @param message
     * @return message
     */

    public function destroy_record()
    {
        $id = request()->id;
        $table_name = request()->tbl_name;
        $column_name = request()->column_name;
        $message = request()->message;

        if($column_name == false) {#column not have data
            $data = DB::table($table_name)->where('id', $id)->first();
            $data->delete();
        } else {
            #column have data
            $folder_name = "submenu_icon";
            $data = DB::table($table_name)->where('id', $id)->first();

            if ($table_name == 'submenus' && $column_name == 'icon') {
                $image_path = public_path() . '/' . $folder_name . '/' . $data->icon;  // Value is not URL but directory file path
                if (file_exists($image_path))  @unlink($image_path);
            }
            $data = DB::table($table_name)->where('id', $id)->update([$column_name => null]);

        }

        return response()->json([
            'success' => true,
            'status' => $message,
        ]);
    }

    public function changeLanguage(){
        if (request()->langid == 'de') $lang_code = Language::German;
        if (request()->langid == 'en') $lang_code = Language::English;
        if(request()->langid == 'pt') $lang_code = Language::Portuguese;
        if(request()->langid == 'it') $lang_code = Language::Italian;
        else $lang_code = request()->langid;
        #remove old locale session
        Session::forget('locale');
        #language set
        Session::put('locale', $lang_code);

        return response()->json([
            'success' => true,
            'code' => Session::get('locale')
        ]);
    }

    public function staffDelete(){
        $staff = Staff::where('user_id',request()->user_id)->delete();
        $id = request()->user_id;
        User::find($id)->update(['user_type'=>0]);

        activityLogTrack(3, "User Id : {$id} at Staff Module", "Benutzeridentifikation : {$id} um Personalmodul ",'User Staff', 'users_staff');#AL
        return response()->json([
            'success' => true
        ]);
    }
    public function userDeleteFromGroup(){
        $user = DB::table('group_inusers')->where('user_id', request()->user_id)->delete();
        $id = request()->user_id;
        // activityLogTrack(3, "User Id : {$id} at Staff Module", "Benutzeridentifikation : {$id} um Personalmodul ",'User Staff', 'users_staff');#AL
        return response()->json([
            'success' => true
        ]);
    }

    public function get_group_users($id)
    {
        $group = Group::with('assignUser:users.id')->find($id);
        $ids = [];
        if(count($group->assignUser) > 0)
            foreach($group->assignUser as $user) $ids[] = $user->id;

        $users = Auth::user()->staffs->whereNotIn('id',$ids)->all();
        return response()->json([
            'success' => true,
            'users' => $users
        ]);
    }

    public function saveAssignStaffForGroup(){
        $userIDS = request()->userID;
        $groupId = request()->groupID;
        if(is_array($userIDS) && !empty($userIDS)){
            foreach($userIDS as $id){
                $user = User::findOrFail($id);
                $check = StaffInGroup::where('group_id', $groupId)->where('user_id',$id)->exists();
                if($check){
                    $duplicate[] = $user;
                }else{
                    $users[] = $user;
                    $assign = new StaffInGroup();
                    $assign->user_id = $id;
                    $assign->group_id = $groupId;
                    $assign->save();
                }

            }
        }
        return response()->json([
            'success' => true,
            'users' => $users,
            'duplicate' => $duplicate
        ]);


    }

    public function removeStaffFromAssign($id,$gid){
        $status = false;
        $check = StaffInGroup::where('group_id', $gid)->where('user_id', $id);
        if($check->exists()){
            $check->delete();
            if($check) User::find($id)->update(['user_type'=>0]);
            $status = true;
        }
        return response()->json([
            'success' => $status
        ]);
    }

    /**
     * get cart content based on id
     * @param id
     * @return list
     */
    public function getCartContent($id){
        $packageDate = getCartContent($id);

        return response()->json([
            'success' => true,
            'contents' => $packageDate->cartcontents
        ]);
    }

    /**
     * add new group
     * @param name
     * @return list
     */
    function addGroup(){
        try {
            $group =  new Group();
            $group->group_name = request()->group_name;
            $group->group_type_id =  request()->group_type_id;
            $group->save();
            return response()->json([
                'success' => true,
                'messege' => 'Group Add Successfully',
                "group"  => $group,
            ]);
        } catch (\Exception  $e) {
            return response()->json([
                'success' => false,
                'messege' => $e->getMessage(),
            ]);
        }
    }

    public function getDataFroDashRight($type)
    {
        try {
            if ($type == 2) {
                    $loginUser = User::find(getUserId() ?? Auth::id());
                    if ($loginUser->user_type == 2 || $loginUser->user_type == 1)
                        $pdfs = DB::table('all_pdfs')->where('creator_id',getUserId() ?? Auth::id())->where('pdf_type','!=','Normal')->orderBy('created_at','DESC')->limit(100)->get();
                    else
                        $pdfs =  DB::table('all_pdfs')->where('fuser_id',getUserId() ?? Auth::id())->where('pdf_type','!=','Normal')->orderBy('created_at','DESC')->limit(100)->get();

                    return [
                        'pdf_list' => view('Frontend.Section.normal_pdf_view', ['data' => $pdfs])->render()
                    ];
            } else if ($type == 3) {
                $result = DB::table('cart_package_users')
                ->select('cart_packages.package_name','cart_packages.id','cart_packages.created_at')
                ->join('cart_packages','cart_package_users.cart_package_id','cart_packages.id')
                ->where('cart_package_users.user_id',getUserId())
                ->orderBy('cart_packages.created_at','DESC')->limit(100)->get();
                return [
                    'treatment_list' => view('Frontend.Section.treatment_list', ['data' => $result])->render()
                ];

            } else if ($type == 4) {
                $loginUser = User::find(getUserId() ?? Auth::id());
                // Only allow admin users to see users list
                if ($loginUser->user_type == UserRoll::Admin) {
                    $users = User::with(['group', 'language'])
                        ->where('boss_id', getAuthID())
                        ->orderBy('created_at', 'DESC')
                        ->limit(100)
                        ->get();

                    return [
                        'users_list' => view('Frontend.Section.users_list', ['data' => $users])->render()
                    ];
                } else {
                    return [
                        'users_list' => view('Frontend.Section.users_list', ['data' => collect()])->render()
                    ];
                }
            } else if($type == 5){
                $loginUser = User::find(getUserId() ?? Auth::id());
                #DC PDF Showing
                if ($loginUser->boss_id == 0)
                    $dcfpdf = DB::table('all_pdfs')->where('creator_id', Auth::id())->where('pdf_type','Normal')->orderBy('created_at','DESC')->limit(100)->get();
                else
                    $dcfpdf = DB::table('all_pdfs')->where("fuser_id", getUserId())->where('pdf_type','Normal')->orderBy('created_at', 'desc')->limit(100)->get();

                return [
                    'dcpdf_list' => view('Frontend.Section.dc_pdf_view', ['data' => $dcfpdf])->render()
                ];
            }
        } catch (\Exception  $e) {
            return response()->json([
                'success' => false,
                'messege' => $e->getMessage(),
            ]);
        }
    }

    public function getSaveCartModal(){
        // $allSaveCart = getCartPackagesById(getUserId());
        $allSaveCart = DB::table('cart_package_users')
        ->select('cart_packages.package_name','cart_packages.id','cart_packages.created_at')
        ->join('cart_packages','cart_package_users.cart_package_id','cart_packages.id')
        ->where('cart_package_users.user_id',getUserId())
        ->orderBy('cart_packages.created_at','DESC')
        ->limit(100)
        ->get();
        return [
            'save_cart' => view('Frontend.modal.SaveCart.save_cart_view', ['allSaveCart' => $allSaveCart])->render()
        ];
    }

}


