<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Menu extends Component
{
    public $data;
    public $lta_days;
    public $addUserStatus;
    public $auth;
    public $subusers;
    public $farbklang = false;

    public function mount($data = [])
    {
        $this->data = $data;
        $this->getMenuInfo();
    }

    private function getMenuInfo()
    {
        $userDetails = getUserDetails();
        $this->addUserStatus = (maxAddUser() <= 0) ? false : true;
        $this->auth = Auth::user();
        $this->subusers = getSubUser();
        $data['filterid'] = !isset($data['filterid']) ? $userDetails?->userfilter()?->first(['filter_type'])?->filter_type : $data['filterid'];
        $this->lta_days = DB::table('lta_days')->orderBy('days', 'ASC')->get(['id','days']);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.menu');
    }
}
