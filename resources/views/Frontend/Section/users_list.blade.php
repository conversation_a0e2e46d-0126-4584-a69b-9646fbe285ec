{{-- Show Users List for Admin --}}
<style>
    .users-list li p>*:hover{
        color:#02BC77 !important
    }
    .user-single-box {
        margin-bottom: 10px;
    }
    .usb-photo img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
    .usb-content p {
        margin: 5px 0;
        font-weight: 500;
    }
    .user-actions {
        display: flex;
        gap: 5px;
        margin-top: 5px;
    }
    .user-actions a {
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
        text-decoration: none;
    }
    .btn-switch {
        background-color: #007bff;
        color: white;
    }
    .btn-edit {
        background-color: #28a745;
        color: white;
    }
    .btn-delete {
        background-color: #dc3545;
        color: white;
    }
</style>

<ul class="users-list web-kit-scroll">
    @if($data && count($data) > 0)
        @foreach ($data as $user)
            <li>
                <div class="user-single-box">
                    <div class="d-flex align-items-center">
                        <div class="usb-photo mr-3">
                            <img src="{{ getProfileImage($user) }}" alt="{{ $user->fullName }}" class="rounded-circle">
                        </div>
                        <div class="usb-content flex-grow-1">
                            <p class="mb-1"><strong>{{ $user->fullName }}</strong></p>
                            <p class="mb-1 text-muted small">{{ $user->email }}</p>
                            <p class="mb-1 text-muted small">
                                {{ trans('action.user_registered') }}: {{ \Carbon\Carbon::parse($user->created_at)->format('d.m.Y') }}
                            </p>
                            @if($user->group && count($user->group) > 0)
                                <p class="mb-1 text-info small">
                                    {{ trans('action.user_groups') }}: {{ $user->group[0]->name ?? '' }}
                                </p>
                            @endif
                        </div>
                        <div class="user-actions">
                            <a href="javascript:void(0)" onclick="switch_user({{ $user->id }})"
                               class="btn-switch" title="{{ trans('action.switch') }}">
                                <i class="fas fa-exchange-alt"></i>
                            </a>
                            <a href="{{ route('users.edit', $user->id) }}" 
                               class="btn-edit" title="{{ trans('action.edit') }}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="javascript:void(0)" onclick="deleteSubUser({{ $user->id }})" 
                               class="btn-delete" title="{{ trans('action.delete') }}">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </li>
        @endforeach
        
        {{-- Add new user option --}}
        @if(maxAddUser() > 0)
            <li>
                <div class="user-single-box text-center">
                    <a href="{{ route('users.show') }}" class="d-block p-3 border border-dashed rounded">
                        <div class="usb-icon mb-2">
                            <i class="fa fa-plus fa-2x text-primary"></i>
                        </div>
                        <div class="usb-content">
                            <p class="mb-0 text-primary">{{ trans('action.create_new_user') }}</p>
                        </div>
                    </a>
                </div>
            </li>
        @endif
    @else
        <li>
            <div class="user-single-box text-center">
                <p class="text-muted">{{ trans('action.no_records_available') }}</p>
                @if(maxAddUser() > 0)
                    <a href="{{ route('users.show') }}" class="btn btn-primary btn-sm mt-2">
                        <i class="fa fa-plus"></i> {{ trans('action.create_new_user') }}
                    </a>
                @endif
            </div>
        </li>
    @endif
</ul>
