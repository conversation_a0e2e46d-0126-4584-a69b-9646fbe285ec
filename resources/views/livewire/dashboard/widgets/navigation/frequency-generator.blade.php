<div>
    <style>
        /*
          Allow the frequency generator's dropdown to overflow its container.
          We target the specific grid item by its widget ID to avoid side effects on other widgets.
        */
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

    <!-- Direct Content - No Tab Navigation -->
    <div class="card frequencyGeneratorTab fade show" 
         id="nav-widget-frequencyGeneratorTab-{{ $widget['id'] ?? 'default' }}" 
         data-livewire-component="{{ $this->getId() }}"
    >
        <div class="arrow"></div>
        <div class="card-body">
            <form action="">
                <div class="form-group topic">
                    <textarea name="" 
                        id="nav-widget-frequency-topic-{{ $widget['id'] ?? 'default' }}" 
                        rows="2" 
                        class="form-control" 
                        wire:model.defer="topicText"
                        placeholder="{{ trans('action.topic_name_placeholder') }}">{{ $topicText }}</textarea>
                </div>
                <div class="frequency-time-controls">
                    <div class="frequency-input-group">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="nav-widget-frequency-hz-{{ $widget['id'] ?? 'default' }}"
                                   style="border: 0 !important;"
                                   min="250" max="20000" step="50" 
                                   placeholder="{{ trans('action.frequency_placeholder') }}" 
                                   wire:model.defer="frequencyHz"
                                   value="{{ $frequencyHz }}">
                            <span class="input-group-text">{{ trans('action.unit_hertz') }}</span>
                            <button type="button" class="btn btn-warning harmonics-btn" 
                                    title="{{ __('action.calculate_harmonics') }}" 
                                    data-bs-toggle="tooltip" 
                                    data-bs-placement="top">
                                <i class="fas fa-wave-square"></i>
                                <span class="click-indicator d-none"><i class="fas fa-mouse-pointer"></i></span>
                            </button>
                        </div>
                        <small class="form-text d-none">{{ trans('action.frequency_placeholder') }}</small>
                    </div>
                    <div class="time-input-group">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="nav-widget-frequency-time-{{ $widget['id'] ?? 'default' }}" 
                                   style="border: 0 !important;"
                                   min="5" max="3600" step="1" 
                                   placeholder="{{ trans('action.seconds_placeholder') }}"
                                   wire:model.defer="frequencyTime"
                                   value="{{ $frequencyTime }}">
                            <span class="input-group-text">{{ trans('action.unit_seconds') }}</span>
                        </div>
                        <small class="form-text d-none">{{ trans('action.seconds_placeholder') }}</small>
                    </div>
                </div>
            </form>
            <div class="text-center frequency-btns">
                <button type="button" 
                        class="btn btn-primary icon"
                        onclick="handleAddToCart('{{ $widget['id'] ?? 'default' }}', @this)">
                    {{ trans('action.cart') }}
                </button>
            </div>
        </div>
    </div>

    <script>
    // Set up global variables for FrequencyGenerator widget
    document.addEventListener('DOMContentLoaded', function() {
        // Only initialize if not already initialized to prevent overwriting
        if (!window.routes) {
            window.routes = {
                calculateFrequency: '{{ route("calculation.calculateFrequency") }}'
            };
        } else if (!window.routes.calculateFrequency) {
            window.routes.calculateFrequency = '{{ route("calculation.calculateFrequency") }}';
        }
        
        if (!window.csrfToken) {
            window.csrfToken = '{{ csrf_token() }}';
        }
        
        if (!window.biorhythDetails) {
            window.biorhythDetails = {
                min: {{ $biorythDetails->gs_min_price ?? 5 }},
                max: {{ $biorythDetails->gs_max_price ?? 3600 }}
            };
        }
        
        const frequencyTranslations = {
            calculate_harmonics: '{{ __('action.calculate_harmonics') }}',
            frequency_too_low: '{{ __('action.frequency_too_low') }}',
            frequency_calculation_in_progress: '{{ __('action.frequency_calculation_in_progress') }}',
            frequency_placeholder: '{{ __('action.frequency_placeholder') }}',
            seconds_placeholder: '{{ __('action.seconds_placeholder') }}',
            unit_hertz: '{{ __('action.unit_hertz') }}',
            unit_seconds: '{{ __('action.unit_seconds') }}',
            frequency_time_both_required: '{{ __('action.frequency_time_both_required') }}',
            invalid_frequency: '{{ __('action.invalid_frequency') }}',
            invalid_time: '{{ __('action.invalid_time') }}',
            frequency_generator: '{{ __('action.frequency_generator') }}',
            note_couldnt_save: '{{ __('action.note_couldnt_save') }}',
            cart_max_allow_alert: '{{ __('action.cart_max_allow_alert') }}',
            processing: '{{ __('action.processing') }}',
            topic_cart_save: '{{ __('action.topic_cart_save') }}',
            error_occurred: '{{ __('action.error_occurred') }}'
        };
        window.translations = Object.assign(window.translations || {}, frequencyTranslations);
        
        // Set component ID for this widget instance
        window.FrequencyGeneratorComponentId = window.FrequencyGeneratorComponentId || {};
        window.FrequencyGeneratorComponentId['{{ $widget['id'] ?? 'default' }}'] = '{{ $this->getId() }}';
        
        // Ensure handleAddToCart is available
        if (typeof window.handleAddToCart === 'undefined') {
            window.handleAddToCart = function(widgetId, livewireComponent) {
                console.log('Using fallback handleAddToCart for widgetId:', widgetId);
                
                // Basic cart validation
                const topicInput = document.getElementById(`nav-widget-frequency-topic-${widgetId}`);
                const freqInput = document.getElementById(`nav-widget-frequency-hz-${widgetId}`);
                const timeInput = document.getElementById(`nav-widget-frequency-time-${widgetId}`);
                
                const content = topicInput?.value || '';
                const frequency = freqInput?.value || '';
                const time = timeInput?.value || '';
                
                // Basic validation
                if (!content.trim()) {
                    alert(window.translations?.note_couldnt_save || 'Please enter a topic');
                    return;
                }
                
                if (!frequency || !time) {
                    alert(window.translations?.frequency_time_both_required || 'Both frequency and time are required');
                    return;
                }
                
                if (frequency && (frequency < 250 || frequency > 20000)) {
                    alert(window.translations?.invalid_frequency || 'Invalid frequency range (250-20000 Hz)');
                    return;
                }
                
                if (time && (time < 5 || time > 3600)) {
                    alert(window.translations?.invalid_time || 'Invalid time range (5-3600 seconds)');
                    return;
                }
                
                // Use AJAX to add to cart like original system
                $.ajax({
                    type: 'POST',
                    url: '/Sajax/add2Cart',
                    data: {
                        ana_id: 1,
                        name: content,
                        submenu_id: '',
                        proID: '',
                        calculation: '',
                        male: '',
                        heart: '',
                        price: '',
                        causes_id: '',
                        medium_id: '',
                        tipp_id: '',
                        color: '',
                        type: 'Topic',
                        frequency: frequency,
                        time: time,
                        _token: window.csrfToken || $('meta[name="csrf-token"]').attr('content')
                    },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            alert('Frequency added to cart successfully');
                        } else {
                            alert(data.message || 'Failed to add to cart');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Cart error:', error);
                        alert('Error adding to cart');
                    }
                });
            };
        }
    });
    </script>

    <!-- FrequencyGenerator Widget Specific JavaScript -->
    <script src="{{ asset('js/dashboard/widgets/frequency-generator-widget.js') }}" defer></script>
</div>
