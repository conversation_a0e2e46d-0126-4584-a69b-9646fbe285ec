<div>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">
    <div class="navigation-buttons">
        @if(($farbklang && $auth->user_type == \App\Enums\UserRoll::Therapist) || !$farbklang)
        <div class="livewire-menu-container" style="position: static; display: block;">
            <a class="dropdown-item livewire-menu-item @if($farbklang) dashActive_btn @endif" data-url='{{ url('/') }}'
            href="javascript:void(0)" data-name="{{trans('action.system_setting')}}" data-tab="1" id="livewire-menu-1">
            <i class="ion ion-md-settings ">&nbsp;</i><span>{{trans('action.system_setting')}}</span>
            </a>
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.last_treatment')}}" data-tab="3" id="livewire-menu-3">
            <i class="ion ion-md-cart">&nbsp;</i><span>{{trans('action.last_treatment')}}</span>
            </a>
        
            @if($auth->user_type == \App\Enums\UserRoll::Therapist || $auth->user_type == \App\Enums\UserRoll::Staff)
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.pdf_heading')}}" data-tab="2" id="livewire-menu-2"><i
                class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.pdf_heading')}}</span></a>
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.digitalcon&focusPDF')}}" data-tab="5" id="livewire-menu-5"><i
                class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.digitalcon&focusPDF')}}</span></a>
            @endif
            @if ($auth->user_type == \App\Enums\UserRoll::Admin)
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.user')}}" data-tab="4" id="livewire-menu-4"><i
                class="ion ion-ios-contacts">&nbsp;</i><span>{{trans('action.user')}}</span></a>
            @endif
        </div>
        @endif
        @if(is_subclass_of(static::class, \Livewire\Component::class))
        <select class="custom-select border-none mt-2 mt-sm-0 tabs-itemBox-Style" id="longday"
            wire:model.live="selectedLongDay">
            <option value="">{{ __('action.lta_choose_days') }}</option>
            @if(count($lta_days) > 0)
            @foreach($lta_days as $days)
            <option value="{{$days->days}}" @if($selectedLongDay==$days->days) selected @endif>{{$days->days}}
                {{__('action.lta_days') }}</option>
            @endforeach
            @endif
        </select>
        @endif
        @if($farbklang && $auth->user_type == \App\Enums\UserRoll::Admin)
        <button type="button" class="tabs-itemBox-Style" id="users_button"><i
                class="fas fa-users">&nbsp;</i><span>{{trans('action.user')}}</span></button>
        @endif
    </div>
    <div class="tab-contents">
        <div id="tab-1" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body web-kit-scroll">
                    <ul class="sys-setting">
                        <li>
                            <p>
                                {{trans('action.calculation_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-1 mr-2">{{trans('action.year')}}</div>
                                    <label class="switcher switcher-lg switcher-success m-0">
                                        <input type="checkbox" onclick="changeYearMonth()" id="changeYearMonth"
                                            @if($userDetails->calculation_with == 1) {{'checked'}} @endif
                                        class="switcher-input">
                                        <span class="switcher-indicator">
                                            <span class="switcher-yes"></span>
                                            <span class="switcher-no"></span>
                                        </span>
                                    </label>
                                    <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.date_system_dashboard')}}
                            </p>
    
                            <div class="right-side">
                                <div class="right-side ml-3 ml-sm-0">
                                    <input type="text" class="form-control datef" data-date="{{ $userDetails->datumcore }}"
                                        id="datePicker" onchange="dateChange()" placeholder="mm/dd/yyyy"
                                        value="{{ \Carbon\Carbon::parse($userDetails->datumcore)->toDateString() }}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.sorting_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <select class="custom-select" id="changeShowFilter" onchange="changeShowFilter()">
                                    <option @if ($data['filterid']==1) {{ 'selected' }} @endif value="1">A-Z</option>
                                    <option @if ($data['filterid']==2) {{ 'selected' }} @endif value="2">Z-A</option>
                                    <option @if ($data['filterid']==3) {{ 'selected' }} @endif value="3">1-100</option>
                                    <option @if ($data['filterid']==4) {{ 'selected' }} @endif value="4">100-1</option>
                                </select>
                            </div>
                        </li>
                    </ul>
    
                </div>
            </div>
        </div>
    
        <div id="tab-2" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="show_normal_pdfs">
    
                </div>
            </div>
        </div>
    
        <div id="tab-3" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="save_treatment_cart_list" style="padding: 15px 10px 15px 15px;">
                </div>
            </div>
        </div>
    
        <div id="tab-4" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body web-kit-scroll">
                    <div id="show_users_content">
                        <!-- User content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    
        <div id="tab-5" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body web-kit-scroll">
                    <div id="show_digital_content">
                        <!-- Digital content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Livewire menu functionality with unique classes
        function initializeLivewireMenu() {
            $('.livewire-menu-item').off('click').on('click', function () {
                var changename = $(this).data('name');
                var dropdown = $(this).data('tab');
                
                // Remove active class from all livewire menu items
                $('.livewire-menu-item').removeClass('dashActive_btn');
                // Add active class to clicked item
                $(this).addClass('dashActive_btn');
                
                if(dropdown == 1 || dropdown == 4){
                    $('#topicTab').hide();
                    $('.tab-content').hide();
                    $('#tab-' + dropdown).removeClass('hide').show();
                }else{
                    $('#topicTab').hide();
                    $('.tab-content').hide();
                    $('#tab-' + dropdown).removeClass('hide').show();

                    // Check if content needs to be loaded for tabs 2, 3, 4, and 5
                    var needsLoading = false;
                    if(dropdown == 2 && $('#show_normal_pdfs').children().length == 0) {
                        needsLoading = true;
                    } else if(dropdown == 3 && $('#save_treatment_cart_list').children().length == 0) {
                        needsLoading = true;
                    } else if(dropdown == 4 && $('#show_users_content').children().length == 0) {
                        needsLoading = true;
                    } else if(dropdown == 5 && $('#show_digital_content').children().length == 0) {
                        needsLoading = true;
                    }

                    if(needsLoading) {
                        loadTabContent(dropdown);
                    }
                }
            });
        }
        
        function loadTabContent(tabId) {
            // Show loading indicator
            $('#tab-' + tabId + ' .card .card-body').html(`<div class="preloader" > <img style="top:20px !important" src="/images/Fill-4.png" alt="">  </div>`).attr('style','min-height:270px');

            // Make AJAX call to load content
            $.ajax({
                type: 'GET',
                url: '{{ url("/") }}/Sajax/get_data/' + tabId,
                dataType: "json",
                success: function(res) {
                    $('#tab-' + tabId + ' .card .card-body').removeAttr('style').empty();

                    if(tabId == 2) {
                        // Load PDF content
                        $('#show_normal_pdfs').empty();
                        $('#show_normal_pdfs').append(res.pdf_list);
                    } else if(tabId == 3) {
                        // Load treatment content
                        $('#save_treatment_cart_list').empty();
                        $('#save_treatment_cart_list').append(res.treatment_list);
                    } else if(tabId == 4) {
                        // Load users content
                        $('#show_users_content').empty();
                        $('#show_users_content').append(res.users_list);
                    } else if(tabId == 5) {
                        // Load digital content
                        $('#show_digital_content').empty();
                        $('#show_digital_content').append(res.dcpdf_list);
                    }
                },
                error: function(data) {
                    console.log('Error loading tab content:', data);
                    $('#tab-' + tabId + ' .card .card-body').removeAttr('style').html('<p class="text-center text-muted">Error loading content</p>');
                }
            });
        }
        
        // Initialize on page load
        $(document).ready(function() {
            initializeLivewireMenu();
        });
        
        // Re-initialize after Livewire updates
        document.addEventListener('livewire:navigated', function() {
            initializeLivewireMenu();
        });
        
        if (typeof Livewire !== 'undefined') {
            Livewire.hook('morph.updated', () => {
                initializeLivewireMenu();
            });
        }
    </script>
    <script src="{{ asset('js/dashboard/right-panel.js') }}"></script>
</div>
